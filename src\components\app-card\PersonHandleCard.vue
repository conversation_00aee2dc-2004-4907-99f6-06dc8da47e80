<script lang="ts" setup>
import { M11 } from "~/api/performance";

const router = useRouter();
const { performance } = useFetchApi();
const { data } = performance.statisticsData<"m11">("m11");

function percentage(value: number): number {
  if (Number.isNaN(value) || !data.value?.data.length) return 0;
  return (value / Number(data.value.data[0].value)) * 100;
}
function handleRowClick(row: M11) {
  router.push({
    name: "incident_view",
    query: {
      key: "m10_m11",
      name: window.encodeURI("人员待办数排行"),
      startDate: performance.statisticsDate.value.startDate,
      endDate: performance.statisticsDate.value.endDate,
      userId: row.key,
    },
  });
}
</script>

<template>
  <Card title="人员待办数排行" style="--max-height-content: 460px">
    <el-table :data="data?.data" width="100%" show-overflow-tooltip @row-click="handleRowClick">
      <el-table-column prop="name" label="姓名" width="100" />
      <el-table-column prop="value" label="数量" width="100" />
      <el-table-column>
        <template #default="{ row }">
          <el-progress :percentage="percentage(Number(row.value))" :stroke-width="14" :show-text="false" />
        </template>
      </el-table-column>
    </el-table>
  </Card>
</template>

<style lang="less" scoped></style>
