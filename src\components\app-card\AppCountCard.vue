<script lang="ts" setup>
const page = usePageValue({ pageSize: 5 });
const { performance } = useFetchApi();
const { data } = performance.appQuantityList(page);
</script>

<template>
  <Card title="应用数量排序">
    <el-table :data="data?.data" style="width: 100%">
      <el-table-column prop="appName" label="应用名称" />
      <el-table-column prop="numberOfClicks" label="应用数量" width="100" align="center" />
    </el-table>
  </Card>
</template>

<style lang="less" scoped>
@import "~/styles/table.less";
</style>
