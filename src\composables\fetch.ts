import { createFetch, type UseFetchOptions, type UseFetchReturn } from "@vueuse/core";
import { useOidcUser } from "~/auth/hooks";
import { ElNotification } from "element-plus";
import { isArray } from "lodash-es";
import { useAuth, useOidcStore } from "vue3-oidc";
import "element-plus/es/components/notification/style/css";

const user = useOidcUser();
const { state } = useOidcStore();
const { refreshToken } = useAuth();
enum HttpStatus {
  NO_AUTH = 401,
  Not_Found = 404,
}
export interface CreateServiceOptions {
  /**
   * Default Options for the useFetch function
   */
  options?: UseFetchOptions;
  /**
   * Options for the fetch request
   */
  fetchOptions?: RequestInit;
}

export function createService(baseUrl: string, serviceOptions: CreateServiceOptions = {}) {
  const options: UseFetchOptions = {
    refetch: true,
    timeout: 30 * 1000,
    beforeFetch({ options, cancel }) {
      if (!user.value?.access_token) {
        cancel();
      }

      options.headers = {
        ...options.headers,
        Authorization: `Bearer ${user.value?.access_token}`,
      };

      options.body = isArray(options.body) ? JSON.stringify(options.body) : options.body;

      return {
        options,
      };
    },
    afterFetch(ctx) {
      if (ctx.data?.code !== 1) {
        ElNotification({
          type: "error",
          title: "请求 Error",
          message: ctx.data.message ? ctx.data.message : "API请求错误,请检查网络",
        });
      }
      return ctx;
    },
    onFetchError(ctx) {
      if (ctx.response?.status === HttpStatus.NO_AUTH) {
        ElNotification({
          type: "error",
          title: "请求 401",
          message: "权限认证失败!",
        });
        refreshToken(
          undefined,
          (user) => {
            console.log("fetch refresh token is success", user);
          },
          (err) => {
            console.log(err);
            state.value.userManager?.signoutRedirect();
          },
        );
      } else if (ctx.response?.status === HttpStatus.Not_Found) {
        ElNotification({
          type: "error",
          title: "请求 404",
          message: "请求地址错误,请检查API URL",
        });
      }
      return ctx;
    },
    ...serviceOptions.options,
  };

  const fetchOptions: RequestInit = {
    headers: {
      "Content-Type": "application/json",
    },
    ...serviceOptions.fetchOptions,
  };
  const fetch = createFetch({
    baseUrl: baseUrl,
    options,
    fetchOptions,
  });

  return fetch;
}

/**
 * @fn return to after deconstruction is data
 */
export async function unFetchData<T = any>(
  fetch: UseFetchReturn<T> & PromiseLike<UseFetchReturn<T>>,
): Promise<APIResult<T>> {
  const { data } = (await fetch.then()) as UseFetchReturn<any>;
  return data.value;
}

interface StringLike {
  toString(): string;
}

/**
 * @fn join params
 */
export function paramsSerializer(params: Record<string, StringLike | undefined> | object) {
  if (!params) return;
  const query = new URLSearchParams();
  Object.entries(params).forEach(([k, v]) => {
    const value = v?.toString();
    if (!value) {
      return;
    }
    query.append(k, value);
  });
  return "?" + query.toString();
}

declare global {
  type APIResult<T = any> = {
    code: number | string;
    count: number;
    message: string | undefined;
    data: T;
  };
  interface Page {
    pageIndex: number;
    pageSize: number;
    keyWord?: string;
    sortField?: `${Uppercase<string>}${string}` | "";
    sortType?: "ASC" | "DESC";
    totalCount?: number;
  }
}
