//compatible with low-version browser
import "core-js";
//
import "./style.css";
import "uno.css";
import "~/dayjs";
import App from "./App.vue";
import routes from "~pages";
import { createApp } from "vue";
import { createRouter, createWebHistory } from "vue-router";
import "~/auth";
import { oidcRoutes } from "~/auth/routes";

const app = createApp(App);
const router = createRouter({ routes: routes, history: createWebHistory(__APP_CONFIG__.BASE) });
console.log(router.getRoutes());
oidcRoutes.forEach((route) => router.addRoute(route));
app.use(router);
app.mount("#app");
