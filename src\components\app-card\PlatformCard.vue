<script lang="ts" setup>
import { M1 } from "~/api/performance";
import { CSSProperties } from "vue";

const [DefineTemplate, ReuseTemplate] = createReusableTemplate<{
  data?: string;
  text?: string;
  color?: CSSProperties["color"];
}>();

const router = useRouter();
const { performance } = useFetchApi();
const { data } = performance.statisticsData<"m1">("m1");

function handleClick(item: M1) {
  if (item.key === "m1_v1") {
    navigateTo(window.remoteConfig.sites.studioSite, {
      open: {
        target: "_blank",
      },
    });
  } else {
    router.push({
      name: "incident_view",
      query: {
        key: item.key,
        name: window.encodeURI(item.name),
        startDate: performance.statisticsDate.value.startDate,
        endDate: performance.statisticsDate.value.endDate,
      },
    });
  }
}
</script>

<template>
  <DefineTemplate v-slot="{ data, text, color }">
    <div
      class="h-90px bg-#F7F8FA rounded-10px flex items-center justify-center flex-col hover:animate-pulse transition-all hover:bg-#ebebeb cursor-pointer"
    >
      <div class="text-30px app-count" :style="{ color: color }">{{ data }}</div>
      <div class="text-14px color-#83848B">{{ text }}</div>
    </div>
  </DefineTemplate>
  <Card title="平台使用情况">
    <div class="platform-content grid grid-cols-4 grid-gap-20px">
      <ReuseTemplate
        v-for="item in data?.data"
        :data="item.value"
        :text="item.name"
        :color="item.color"
        @click="handleClick(item)"
      />
    </div>
  </Card>
</template>

<style lang="less" scoped>
@media (min-width: 2000px) {
  .platform-content {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}
@media (max-width: 1200px) {
  .platform-content {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.app-count {
  font-family: sans-serif;
  font-weight: bold;
}
</style>
