import { MaybeRef, MaybeRefOrGetter } from "vue";

const baseURL = window.remoteConfig.hosts.gateway + "/performance";
const $fetch = createService(baseURL);

enum API {
  AppQuantityList = "/api/appQuantity/getList",
  StatisticsData = "/api/statistics/getData",
  GetM16Data = "/api/statistics/getM16Data",
  GetM17Data = "/api/statistics/getM17Data",
  GetM18Data = "/api/statistics/getM18Data",
  GetM19Data = "/api/statistics/getM19Data",
  GetM20Data = "/api/statistics/getM20Data",
}

export function appQuantityList(page: MaybeRef<Page>) {
  return $fetch(API.AppQuantityList).post(page).json<APIResult<[]>>();
}

export const statisticsDate = useStorage<{ startDate: string | undefined; endDate: string | undefined }>(
  "statisticsDate",
  {
    startDate: undefined,
    endDate: undefined,
  },
  localStorage,
);

export function statisticsData<T extends StatisticsKeys>(moduleName: T) {
  return $fetch(
    computed(
      () =>
        API.StatisticsData +
        paramsSerializer({
          moduleName,
          startDate: statisticsDate.value.startDate,
          endDate: statisticsDate.value.endDate,
        }),
    ),
  )
    .post()
    .json<APIResult<StatisticsData[T]>>();
}

export function getM16Data(params: MaybeRefOrGetter<M16DataParams>) {
  return $fetch(computed(() => API.GetM16Data + paramsSerializer(resolveUnref(params))))
    .post()
    .json<APIResult<Array<M5>>>();
}

export function getM17Data(params: MaybeRefOrGetter<M17DataParams>) {
  return $fetch(computed(() => API.GetM17Data + paramsSerializer(resolveUnref(params))))
    .post()
    .json<APIResult<Array<M7>>>();
}

export function getM18Data(params: MaybeRefOrGetter<M18DataParams>) {
  return $fetch(computed(() => API.GetM18Data + paramsSerializer(resolveUnref(params))))
    .post()
    .json<APIResult<Array<M8>>>();
}

export function getM19Data(params: MaybeRefOrGetter<M19DataParams>) {
  return $fetch(computed(() => API.GetM19Data + paramsSerializer(resolveUnref(params))))
    .post()
    .json<APIResult<Array<M9>>>();
}

export function getM20Data(params: MaybeRefOrGetter<M20DataParams>) {
  return $fetch(computed(() => API.GetM20Data + paramsSerializer(resolveUnref(params))))
    .post()
    .json<APIResult<Array<M13>>>();
}

export interface BaseData {
  key: string;
  sort: number;
  name: string;
  value: string;
}

export interface StatisticsData {
  m1: Array<M1>;
  m2: Array<M2>;
  m3: Array<M3>;
  m4: Array<M4>;
  m5: Array<M5>;
  m6: Array<M6>;
  m7: Array<M7>;
  m8: Array<M8>;
  m9: Array<M9>;
  m10: Array<M10>;
  m11: Array<M11>;
  m12: Array<M12>;
  m13: Array<M13>;
  m14: Array<M14>;
  m15: Array<M15>;
}
export interface M1 extends BaseData {
  color: string;
  key: `m1_v${number}`;
}
export interface M2 extends BaseData {}
export interface M3 extends BaseData {}
export interface M4 extends BaseData {}
export interface M5 extends BaseData {}
export interface M6 extends BaseData {}
export interface M7 extends BaseData {}
export interface M8 extends BaseData {}
export interface M9 extends BaseData {}
export interface M10 extends BaseData {}
export interface M11 extends BaseData {}
export interface M12 extends BaseData {}
export interface M13 extends BaseData {}
export interface M14 extends BaseData {}
export interface M15 extends BaseData {}
export type StatisticsKeys = keyof StatisticsData;

export interface MData {
  startDate: string;
  endDate: string;
  pageIndex: number;
  pageSize: number;
}

export interface M16DataParams extends MData {
  lowerBound?: number;
  upperBound?: number;
}

export interface M17DataParams extends MData {
  departmentId: string;
}

export interface M18DataParams extends MData {
  departmentId: string;
}

export interface M19DataParams extends MData {
  departmentId: string;
}

export interface M20DataParams extends MData {
  departmentId: string;
}
