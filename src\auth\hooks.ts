import type { IdpTag, User } from "./types";
import { useOidcStore } from "vue3-oidc";

const { state, actions } = useOidcStore<User>();

export function useOidcUser() {
  return computed(() => state.value.user);
}

export function useOidcUserProfile() {
  return computed(() => state.value.user?.profile);
}

export function useIdpTag(): IdpTag {
  const ua = window.navigator.userAgent.toLowerCase();
  if (ua.match(/MicroMessenger/i)?.toString() === "micromessenger" || ua.match(/_SQ_/i)?.toString() === "_sq_") {
    return "idp:Weixin";
  }
  if (ua.indexOf("dingtalk") > -1) {
    return "idp:DingTalk";
  } else {
    return window.remoteConfig.oidcSettings.acr_values;
  }
}
