<script lang="ts" setup>
import { M8, M18DataParams } from "~/api/performance";

const route = useRoute();
const router = useRouter();
const { performance } = useFetchApi();
const query = route.query as { name: string; startDate: string; endDate: string; departmentId: string };
const params = ref<M18DataParams>({
  startDate: query.startDate,
  endDate: query.endDate,
  departmentId: query.departmentId,
  pageIndex: 1,
  pageSize: 10,
});
const { data } = performance.getM18Data(params);

function percentage(value: number): number {
  if (Number.isNaN(value) || !data.value?.data.length) return 0;
  return (value / Number(data.value.data[0].value)) * 100;
}
function handleRowClick(row: M8) {
  router.push({
    name: "incident_view",
    query: {
      key: "m7_m8",
      name: window.encodeURI("人员耗时排行"),
      startDate: performance.statisticsDate.value.startDate,
      endDate: performance.statisticsDate.value.endDate,
      userId: row.key,
    },
  });
}
</script>

<template>
  <div class="max-sm:px-2 md:px-14 lg:px-20 xl:px-30 2xl:px-45">
    <Title size="17px">{{ decodeURI(query.name) }}</Title>

    <div class="p-10px bg-white rounded-10px">
      <el-table :data="data?.data" width="100%" show-overflow-tooltip @row-click="handleRowClick">
        <el-table-column prop="name" label="姓名" width="150" />
        <el-table-column prop="value" label="耗时" width="150">
          <template #default="{ row }">
            {{ useFormatTime(Number(row.value)) }}
          </template>
        </el-table-column>
        <el-table-column>
          <template #default="{ row }">
            <el-progress :percentage="percentage(Number(row.value))" :stroke-width="14" :show-text="false" />
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        class="mt-20px"
        :page-sizes="[10, 15, 20, 25, 30, 40, 50]"
        layout="prev, pager, next, total, sizes"
        :total="data?.count"
        v-model:page-size="params.pageSize"
        v-model:current-page="params.pageIndex"
      />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
