import { fetchConfig, type FetchOptions } from "@duo-common/config-center";
import { mergeWith } from "lodash-es";
import type { VueOidcSettings } from "vue3-oidc";

interface RemoteConfig {
  hosts: Record<HostName, string>;
  sites: Record<SitesName, string>;
  oidcSettings: VueOidcSettings;
}
enum HostName {
  auth = "auth",
  ocSelector = "ocSelector",
  gateway = "gateway",
  bpm = "bpm",
}

enum SitesName {
  studioSite = "studioSite",
  adminSite = "adminSite",
  bpmSite = "bpmSite",
  formMobileSite = "formMobileSite",
  formWebSite = "formWebSite",
}

const remoteConfigDefine = {
  oidcSettings: {
    redirect_uri: location.origin + __APP_CONFIG__.BASE + "/oidc-callback",
    popup_redirect_uri: location.origin + __APP_CONFIG__.BASE + "/oidc-popup-callback",
    silent_redirect_uri: location.origin + __APP_CONFIG__.BASE + "/silent-renew-oidc",
    loadUserInfo: true,
  },
} as RemoteConfig;

export const loadRemoteConfig = async (options: FetchOptions): Promise<RemoteConfig> => {
  const CONFIG_SERVER = window.CONFIG_SERVER || location.origin + "/config";

  try {
    const remoteConfig = await fetchConfig(CONFIG_SERVER, options, {
      headers: {
        "Content-Type": "application/json",
      },
    }).then((res) => res.json() as Promise<RemoteConfig>);
    return mergeWith(remoteConfigDefine, remoteConfig);
  } catch (error) {
    console.warn("load remote config error.");
  }
  return { ...remoteConfigDefine };
};

declare global {
  interface Window {
    CONFIG_SERVER: string;
    remoteConfig: RemoteConfig;
    __APP_CONFIG__: { BASE: string };
  }
  const __APP_CONFIG__: {
    BASE: string;
  };
}
