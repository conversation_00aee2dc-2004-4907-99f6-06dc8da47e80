<script lang="ts" setup>
import { M4 } from "~/api/performance";

const router = useRouter();
const { performance } = useFetchApi();
const { data } = performance.statisticsData<"m4">("m4");
function handleRowClick(row: M4) {
  router.push({
    name: "department_view",
    query: {
      name: encodeURI("部门应用建设情况"),
      departmentId: row.key,
      startDate: performance.statisticsDate.value.startDate,
      endDate: performance.statisticsDate.value.endDate,
    },
  });
}
</script>

<template>
  <Card title="部门应用建设情况" style="--max-height-content: 380px" border>
    <el-table
      :data="data?.data"
      style="width: 100%"
      show-overflow-tooltip
      @row-click="handleRowClick"
      row-class-name="custom-row"
    >
      <el-table-column prop="name" label="部门名称" />
      <el-table-column prop="value" label="应用数量" width="100" align="center" />
    </el-table>
  </Card>
</template>

<style lang="less" scoped>
@import "~/styles/table.less";
</style>
