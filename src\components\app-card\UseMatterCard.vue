<script lang="ts" setup>
import * as echarts from "echarts";

enum KEY {
  "10次以下" = "10次以下",
  "100次以下" = "100次以下",
  "1000次以下" = "1000次以下",
  "1000次以上" = "1000次以上",
}
const inlineData: Record<KEY, number> = {
  [KEY["10次以下"]]: 0,
  [KEY["100次以下"]]: 0,
  [KEY["1000次以下"]]: 0,
  [KEY["1000次以上"]]: 0,
};
const chartEvent = {
  [KEY["10次以下"]]: () => {
    bounds.lowerBound = 0;
    bounds.upperBound = 10;
  },
  [KEY["100次以下"]]: () => {
    bounds.lowerBound = 10;
    bounds.upperBound = 100;
  },
  [KEY["1000次以下"]]: () => {
    bounds.lowerBound = 100;
    bounds.upperBound = 1000;
  },
  [KEY["1000次以上"]]: () => {
    bounds.lowerBound = 1000;
    bounds.upperBound = 1000000000;
  },
};

const router = useRouter();
const { performance } = useFetchApi();
const { data, onFetchResponse } = performance.statisticsData<"m5">("m5");
const dom = ref<HTMLDivElement>();
const chartInstance = ref<echarts.ECharts>();
const bounds = reactive({
  lowerBound: 0,
  upperBound: 0,
});

onMounted(async () => {
  chartInstance.value = echarts.init(dom.value);
  window.onresize = () => {
    chartInstance.value?.resize();
  };
  chartInstance.value.on("click", (params) => {
    chartEvent[params.name as KEY]?.();
    router.push({
      name: "use_matter",
      query: {
        name: encodeURI(params.name),
        lowerBound: bounds.lowerBound,
        upperBound: bounds.upperBound,
        startDate: performance.statisticsDate.value.startDate,
        endDate: performance.statisticsDate.value.endDate,
      },
    });
  });
});

onFetchResponse(() => {
  if (!data.value?.data.length) return;
  const result: Array<{ name: string; value: number }> = [];
  data.value?.data.forEach((item) => {
    if (Number(item.value) !== 0) {
      inlineData![item.name as KEY] = Number(item.value);
    }
  });
  for (const key in inlineData) {
    result.push({ name: key, value: inlineData[key as KEY] });
  }
  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: "item",
    },
    legend: {
      bottom: 10,
      left: "center",
      data: Object.keys(KEY),
    },
    color: ["#226CF2", "#F13D5F", "#FFA43C", "#16A9FC"],
    series: [
      {
        type: "pie",
        radius: ["40%", "60%"],
        center: ["50%", "50%"],
        selectedMode: "single",
        avoidLabelOverlap: false,
        label: {
          show: true,
        },
        data: result,
      },
    ],
  };
  chartInstance.value && chartInstance.value.setOption(option);
});
</script>

<template>
  <Card title="事项使用情况" style="--max-height-content: 400px">
    <div ref="dom" class="h-380px"></div>
  </Card>
</template>

<style lang="less" scoped></style>
