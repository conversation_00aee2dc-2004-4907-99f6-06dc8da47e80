/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppCountCard: typeof import('./src/components/app-card/AppCountCard.vue')['default']
    Card: typeof import('./src/components/common/Card.vue')['default']
    DepartmentBuild: typeof import('./src/components/app-card/DepartmentBuild.vue')['default']
    DepartmentHandleCard: typeof import('./src/components/app-card/DepartmentHandleCard.vue')['default']
    DepartmentTimeCard: typeof import('./src/components/app-card/DepartmentTimeCard.vue')['default']
    DepartmentTimeoutCard: typeof import('./src/components/app-card/DepartmentTimeoutCard.vue')['default']
    ElAffix: typeof import('element-plus/es')['ElAffix']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    PersonHandleCard: typeof import('./src/components/app-card/PersonHandleCard.vue')['default']
    PersonTimeCard: typeof import('./src/components/app-card/PersonTimeCard.vue')['default']
    PersonTimeoutCard: typeof import('./src/components/app-card/PersonTimeoutCard.vue')['default']
    PlatformCard: typeof import('./src/components/app-card/PlatformCard.vue')['default']
    ProcessHandleCard: typeof import('./src/components/app-card/ProcessHandleCard.vue')['default']
    ProcessTimeCard: typeof import('./src/components/app-card/ProcessTimeCard.vue')['default']
    ProcessTimeoutCard: typeof import('./src/components/app-card/ProcessTimeoutCard.vue')['default']
    QueryCard: typeof import('./src/components/app-card/QueryCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Section: typeof import('./src/components/Section.vue')['default']
    Title: typeof import('./src/components/common/Title.vue')['default']
    UseDepartmentApp: typeof import('./src/components/app-card/UseDepartmentApp.vue')['default']
    UseMatterCard: typeof import('./src/components/app-card/UseMatterCard.vue')['default']
    WorkCard: typeof import('./src/components/app-card/WorkCard.vue')['default']
  }
}
