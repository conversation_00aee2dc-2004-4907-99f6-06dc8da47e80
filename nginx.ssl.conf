# Generated by nginxconfig.io
# See nginxconfig.txt for the configuration share link

user nginx;
pid /var/run/nginx.pid;
worker_processes auto;
worker_rlimit_nofile 65535;

# Load modules
include /etc/nginx/modules-enabled/*.conf;

events {
  multi_accept on;
  worker_connections 65535;
}

http {
  charset utf-8;
  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;
  server_tokens off;
  log_not_found off;
  types_hash_max_size 2048;
  types_hash_bucket_size 64;
  client_max_body_size 16M;

  # MIME
  include mime.types;
  default_type application/octet-stream;

  # Logging
  access_log off;
  error_log /dev/null;

  # SSL
  ssl_session_timeout 1d;
  ssl_session_cache shared:SSL:10m;
  ssl_session_tickets off;

  # Diffie-<PERSON>man parameter for DHE ciphersuites
  ssl_dhparam /etc/nginx/dhparam.pem;

  # Mozilla Intermediate configuration
  ssl_protocols TLSv1.2 TLSv1.3;
  ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

  # OCSP Stapling
  ssl_stapling on;
  ssl_stapling_verify on;
  resolver ******* ******* ******* ******* ************** ************** valid=60s;
  resolver_timeout 2s;

  # Load configs
  include /etc/nginx/conf.d/*.conf;

  # dev.iduo.cc
  server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name dev.iduo.cc;
    root /usr/share/nginx/html;

    # SSL
    ssl_certificate /etc/nginx/ssl/dev.iduo.cc.crt;
    ssl_certificate_key /etc/nginx/ssl/dev.iduo.cc.key;

    # security headers
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: ws: wss: data: blob: 'unsafe-inline'; frame-ancestors 'self';" always;
    add_header Permissions-Policy "interest-cohort=()" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    # . files
    location ~ /\.(?!well-known) {
      deny all;
    }

    # logging
    access_log /var/log/nginx/access.log combined buffer=512k flush=1m;
    error_log /var/log/nginx/error.log warn;

    # index.html fallback
    location / {
      try_files $uri $uri/ /index.html;
    }

    # favicon.ico
    location = /favicon.ico {
      log_not_found off;
    }

    # robots.txt
    location = /robots.txt {
      log_not_found off;
    }

    # Disable HTML caching
    location ~* \.(?:html?)$ {
      add_header Cache-Control "no-cache";
    }

    # assets, media
    location ~* \.(?:css(\.map)?|js(\.map)?|jpe?g|png|gif|ico|cur|heic|webp|tiff?|mp3|m4a|aac|ogg|midi?|wav|mp4|mov|webm|mpe?g|avi|ogv|flv|wmv)$ {
      expires 7d;
    }

    # svg, fonts
    location ~* \.(?:svgz?|ttf|ttc|otf|eot|woff2?)$ {
      add_header Access-Control-Allow-Origin "*";
      expires 7d;
    }

    # gzip
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;
  }

  # HTTP redirect
  server {
    listen 80;
    listen [::]:80;
    server_name dev.iduo.cc;

    # logging
    access_log /var/log/nginx/access.log combined buffer=512k flush=1m;
    return 301 https://dev.iduo.cc$request_uri;
  }
}