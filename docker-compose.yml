version: "3.4"

services:
  iduo-performance:
    image: iduo-performance
    container_name: iduo-performance
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - 5700:80
    restart: always
    network_mode: bridge
    volumes:
      - ./dist:/usr/share/nginx/html/
      # http
      - ./nginx.conf:/etc/nginx/nginx.conf
      # or
      # https SSL
      # - ./nginx.ssl.conf:/etc/nginx/nginx.conf
      # - ./ssl/:/etc/nginx/ssl/
    environment:
      VITE_BASE_URL: /statistic
