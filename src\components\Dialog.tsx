import "element-plus/es/components/dialog/style/css";
import { useToggle } from "@vueuse/core";
import { ElButton, ElDialog, type DialogProps } from "element-plus";
import { isFunction } from "lodash-es";
import { defineComponent, ref, toRefs, type Component, type HTMLAttributes, type VNode } from "vue";

export interface Props {
  content: () => Component | VNode | string;
  footer?: () => Component | VNode | string | undefined;
  footerDefineText?: [string, string];
  beforeClose?: (action: "close" | "confirm") => boolean | Promise<boolean>;
  options?: Partial<DialogProps & HTMLAttributes>;
}

const instance = ref<InstanceType<typeof ElDialog>>();
const [visible, toggle] = useToggle(false);
const propsInstance = ref<Props>({} as Props);
const promiseFn: {
  resolve: (value: unknown) => void;
  reject: (reason?: any) => void;
} = {} as any;

/**
 * 改进版showDialog，请在根节点挂在Dialog组件，然后使用公共方法showDialog
 */
export function showDialog(props: Props) {
  //@ts-ignore
  propsInstance.value = { ...props };
  toggle(true);
  return new Promise((resolve, reject) => {
    promiseFn.resolve = resolve;
    promiseFn.reject = reject;
  });
}

const Dialog = defineComponent(
  () => {
    return () => {
      const { content, footer, footerDefineText, beforeClose, options } = toRefs(propsInstance.value);

      async function handleClose() {
        const done = await handleBeforeClose("close");
        if (done) {
          toggle(false);
          promiseFn.reject(false);
        }
      }
      async function handleConfirm() {
        const done = await handleBeforeClose("confirm");
        if (done) {
          toggle(false);
          promiseFn.resolve(true);
        }
      }
      async function handleBeforeClose(action: "close" | "confirm") {
        let done: boolean = true;
        if (beforeClose?.value && isFunction(beforeClose?.value)) {
          done = await beforeClose.value(action);
        }
        return done;
      }

      const FooterDefine = (
        <>
          <ElButton onClick={() => handleClose()}>{footerDefineText?.value?.[1] ?? "取消"}</ElButton>
          <ElButton type="primary" onClick={() => handleConfirm()}>
            {footerDefineText?.value?.[0] ?? "确定"}
          </ElButton>
        </>
      );
      return (
        <ElDialog
          //@ts-ignore
          ref={instance}
          modelValue={visible.value}
          onUpdate:modelValue={(value) => (visible.value = value)}
          style={{ "--el-border-radius-small": "12px" }}
          destroyOnClose
          {...options?.value}
        >
          {{
            default: content?.value,
            footer: footer?.value ? footer.value : () => FooterDefine,
          }}
        </ElDialog>
      );
    };
  },
  { name: "Dialog" },
);

export default Dialog;
