import dayjs from "dayjs";
import zh from "dayjs/locale/zh";
import isBetween from "dayjs/plugin/isBetween";
import isoWeek from "dayjs/plugin/isoWeek";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import relativeTime from "dayjs/plugin/relativeTime";
import timezone from "dayjs/plugin/timezone";
import toObject from "dayjs/plugin/toObject";
import updateLocale from "dayjs/plugin/updateLocale";
import utc from "dayjs/plugin/utc";

(function setupDayjs() {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  dayjs.extend(relativeTime);
  dayjs.extend(updateLocale);
  dayjs.extend(toObject);
  dayjs.extend(isBetween);
  dayjs.extend(isSameOrAfter);
  dayjs.extend(isSameOrBefore);
  dayjs.extend(isoWeek);

  dayjs.locale(zh);

  dayjs.updateLocale("zh", {
    relativeTime: {
      ...zh.relativeTime,
    },
  });
})();
