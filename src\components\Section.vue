<script lang="ts" setup>
import { Icon } from "@iconify/vue";
import dayjs from "dayjs";
import { DatePickerInstance } from "element-plus";

const range = reactive({
  start: 30,
  end: 0,
});
const datePickerInstance = ref<DatePickerInstance[]>();
const { performance } = useFetchApi();
watchEffect(() => {
  performance.statisticsDate.value.startDate = dayjs().subtract(range.start, "d").format("YYYY-MM-DD HH:mm");
  performance.statisticsDate.value.endDate = dayjs().subtract(range.end, "d").format("YYYY-MM-DD HH:mm");
});

const options = {
  day: {
    text: "日",
    onClick: () => {
      range.start = 1;
    },
  },
  week: {
    text: "周",
    onClick: () => {
      range.start = 7;
    },
  },
  month: {
    text: "月",
    onClick: () => {
      range.start = 30;
    },
  },
  quarter: {
    text: "季",
    onClick: () => {
      range.start = 30 * 3;
    },
  },
  year: {
    text: "年",
    onClick: () => {
      range.start = 30 * 12;
    },
  },
  all: {
    text: "全部",
    onClick: () => {
      range.start = 30 * 12 * 100;
    },
  },
  range: {
    text: "范围",
    onClick: () => {
      range.start = 7;
    },
  },
} as const;
type OptionKey = keyof typeof options;
const route = useRoute();
const router = useRouter();
onMounted(() => {
  if (!route.query.time) {
    router.push({
      name: "index",
      query: {
        time: "month",
      },
    });
  } else {
    options[route.query.time as OptionKey].onClick();
  }
});
const currentOption = ref<OptionKey>((route.query.time as OptionKey) || "month");
const time = dayjs().format("YYYY-MM-DD HH:mm");

function handleClickOption(item: { onClick: () => void }, key: OptionKey) {
  currentOption.value = key;
  if (key === "range") {
    nextTick(() => {
      datePickerInstance.value?.[0].focus(true);
    });
  } else {
    item.onClick();
  }
  router.push({
    name: "index",
    query: {
      time: key,
    },
  });
}
</script>

<template>
  <div
    class="w-full h-45px bg-white flex items-center max-sm:px-2 md:px-14 lg:px-20 xl:px-30 2xl:px-45"
    b-b="1px solid #e7e7e7"
  >
    <div class="flex-1"></div>
    <div class="flex items-center color-#333333 text-14px">
      <div class="flex items-center max-sm:display-none">
        <Icon icon="ph:warning-circle" class="text-18px" style="color: var(--el-color-primary)" />
        <span class="lh-30px ml-4px">数据截止时间 {{ time }} </span>
      </div>

      <div class="flex items-center ml-20px">
        <template v-for="(item, key) in options">
          <div v-if="currentOption === 'range' && key === 'range'">
            <el-date-picker
              ref="datePickerInstance"
              :model-value="[performance.statisticsDate.value.startDate!, performance.statisticsDate.value.endDate!]"
              @update:model-value="
                ([start, end]) => {
                  performance.statisticsDate.value.startDate = start;
                  performance.statisticsDate.value.endDate = end;
                }
              "
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              class="h-26px! ml-10px"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
            />
          </div>

          <el-button v-else :type="currentOption === key ? 'primary' : ''" @click="handleClickOption(item, key)">
            {{ item.text }}
          </el-button>
        </template>

        <!-- <el-button>
          <Icon icon="ic:round-keyboard-arrow-left" class="text-20px cursor-pointer" />
        </el-button>
        <el-button>
          <Icon icon="ic:round-keyboard-arrow-left" class="text-20px cursor-pointer" :rotate="90" />
        </el-button> -->
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.el-button {
  min-width: 26px;
  height: 26px;
  padding: 0;
  padding: 0 4px;
}
</style>
