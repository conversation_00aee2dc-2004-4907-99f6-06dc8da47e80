<script lang="ts" setup>
import { M12 } from "~/api/performance";

const router = useRouter();
const { performance } = useFetchApi();
const { data } = performance.statisticsData<"m12">("m12");

function percentage(value: number): number {
  if (Number.isNaN(value) || !data.value?.data.length) return 0;
  return (value / Number(data.value.data[0].value)) * 100;
}
function handleRowClick(row: M12) {
  router.push({
    name: "department_handle",
    query: {
      name: window.encodeURI("部门待办数排行"),
      startDate: performance.statisticsDate.value.startDate,
      endDate: performance.statisticsDate.value.endDate,
      departmentId: row.key,
    },
  });
}
</script>

<template>
  <Card title="部门待办数排行" style="--max-height-content: 460px">
    <el-table :data="data?.data" width="100%" show-overflow-tooltip @row-click="handleRowClick">
      <el-table-column prop="name" label="部门" width="100" />
      <el-table-column prop="value" label="数量" width="100" />
      <el-table-column>
        <template #default="{ row }">
          <el-progress :percentage="percentage(Number(row.value))" :stroke-width="14" :show-text="false" />
        </template>
      </el-table-column>
    </el-table>
  </Card>
</template>

<style lang="less" scoped></style>
