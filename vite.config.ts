import { resolve } from "path";
import Legacy from "@vitejs/plugin-legacy";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import Unocss from "unocss/vite";
import AutoImport from "unplugin-auto-import/vite";
import ElementPlus from "unplugin-element-plus/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import Components from "unplugin-vue-components/vite";
import { defineConfig, loadEnv } from "vite";
import Inspect from "vite-plugin-inspect";
import Pages from "vite-plugin-pages";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

  return {
    base: process.env.VITE_BASE_URL,
    define: {
      __APP_CONFIG__: { BASE: process.env.VITE_BASE_URL },
    },
    plugins: [
      vue(),
      vueJsx(),
      Legacy({
        targets: ["chrome >= 52"],
        additionalLegacyPolyfills: ["regenerator-runtime/runtime"],
      }),
      Unocss(),
      AutoImport({
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
        ],
        dirs: ["./src/composables"],
        resolvers: [ElementPlusResolver()],
        imports: ["vue", "vue-router", "@vueuse/core"],
        vueTemplate: true,
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
      ElementPlus({}),
      Pages({
        dirs: "src/views",
        routeBlockLang: "json",
      }),
      Inspect(),
    ],
    resolve: {
      alias: [{ find: "~/", replacement: `${resolve(__dirname, "src")}/` }],
    },
    server: {
      host: true,
    },
    esbuild: {
      target: "es2015",
    },
    build: {
      target: "es2015",
      rollupOptions: {
        input: [resolve(__dirname, "index.html"), resolve(__dirname, "src/auth/silent-renew-oidc.html")],
      },
    },
  };
});
