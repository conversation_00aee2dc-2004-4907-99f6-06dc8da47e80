const CRITICAL_VALUE = {
  DAY: 24,
  MONTH: 24 * 30,
  YEAR: 24 * 30 * 12,
};

export function useFormatTime(ms: number) {
  const s = Math.floor(ms / 1000);
  const hours = Math.floor(s / (60 * 60));
  const minute = Math.floor(s / 60);
  if (hours >= CRITICAL_VALUE.YEAR) {
    const year = Math.floor(hours / CRITICAL_VALUE.YEAR);
    return year + "年";
  } else if (hours >= CRITICAL_VALUE.MONTH) {
    const month = Math.floor(hours / CRITICAL_VALUE.MONTH);
    return month + "个月";
  } else if (hours >= CRITICAL_VALUE.DAY) {
    const day = Math.floor(hours / CRITICAL_VALUE.DAY);
    return day + "天";
  } else if (hours < CRITICAL_VALUE.DAY && minute >= 60) {
    return hours + "小时";
  } else if (minute > 10 && minute < 60) {
    return minute + "分钟";
  } else {
    return "10分钟内";
  }
}
