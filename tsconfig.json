{
  "compilerOptions": {
    "target": "ES2015",
    "useDefineForClassFields": true,
    "module": "ES2020",
    "lib": ["ES2015", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": "./",
    "paths": {
      "~/*": ["src/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "env.d.ts",
    "auto-imports.d.ts",
    "components.d.ts"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
