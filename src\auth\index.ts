import { useIdpTag } from "./hooks";
import type { VueOidcSettings } from "vue3-oidc";
import { createOidc, useOidcStore } from "vue3-oidc";

const { state } = useOidcStore();

const oidcSettings: VueOidcSettings = {
  ...window.remoteConfig.oidcSettings,
  acr_values: useIdpTag(),
  loadUserInfo: true,
  automaticSilentRenew: true,
  monitorSession: true,
  silent_redirect_uri: location.origin + __APP_CONFIG__.BASE + "/src/auth/silent-renew-oidc.html",
  onSigninRedirectCallback(user) {
    location.href = unref(state).redirect_uri || "/";
  },
};

createOidc({
  oidcSettings: oidcSettings, //your oidc settings
  auth: true, //if auth is true,will auto authenticate
  events: {}, //your oidc customization callback events
  refreshToken: {
    settings: {
      ...oidcSettings,
      revokeTokenTypes: ["refresh_token"],
      automaticSilentRenew: false,
    },
  },
});
