<script lang="ts" setup>
import { M3 } from "~/api/performance";

const { performance } = useFetchApi();

const { data } = performance.statisticsData<"m3">("m3");

function handleRowClick(row: M3) {
  navigateTo(appBaseURL.value + row.key, {
    open: {
      target: isMobileSize.value ? "_self" : "_blank",
    },
  });
}
</script>

<template>
  <Card title="查询情况" border>
    <el-table :data="data?.data" style="width: 100%" show-overflow-tooltip @row-click="handleRowClick">
      <el-table-column prop="name" label="应用名称" />
      <el-table-column prop="value" label="查询次数" width="100" align="center" />
    </el-table>
  </Card>
</template>

<style lang="less" scoped>
@import "~/styles/table.less";
</style>
