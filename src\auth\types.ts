export interface Job {
  CompanyId: string;
  CompanyName: string;
  DepartmentEnName: string;
  DepartmentId: string;
  DepartmentName: string;
  Id: string;
  IsManager: boolean;
  IsPrimary: boolean;
  JobName: string;
  OrgPath: string;
  RefJobId: string;
}
export interface User {
  Avatar: string;
  Domain: string;
  Email: string;
  Id: string;
  Jobs: Job | Job[];
  Language: string;
  PhoneNumber: string | number;
  RoleIds: string;
  UserAccount: string;
  UserEnName: string;
  UserId: string;
  UserName: string;
  UserRoles: { Id: string; Name: string } | { Id: string; Name: string }[];
  amr: string[];
  auth_time: number | string;
  idp: string;
  preferred_username: string;
  role: string[];
  sid: string;
  sub: string;
}

export type IdpTag = `idp:${"Weixin" | "DingTalk"}` | string | undefined;
