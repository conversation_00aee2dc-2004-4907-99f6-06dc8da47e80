const loadLocalConfig = (url: string, callback: Function) => {
  const scriptEL = document.createElement("script");
  scriptEL.src = url + "?t=" + Date.now();
  scriptEL.onload = () => {
    typeof callback === "function" && callback();
  };
  document.body.appendChild(scriptEL);
};

loadLocalConfig(__APP_CONFIG__.BASE + "/config.js", () => {
  loadRemoteConfig({
    department: "web",
    project: "iduo.performance",
    version: "1",
  }).then((config) => {
    window.remoteConfig = config;
    import("./bootstrap");
  });
});
