import { withQuery } from "ufo";
import { RouteLocationPathRaw, RouteLocationRaw } from "vue-router";

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };
type XOR<T, U> = T | U extends Object ? (Without<T, U> & U) | (Without<U, T> & T) : T | U;
interface NavigateToOptions {
  replace?: boolean;
  open?: OpenOptions;
}

interface OpenOptions {
  target: "_blank" | "_parent" | "_self" | "_top" | (string & {});
  windowFeatures?: OpenWindowFeatures;
}

type OpenWindowFeatures = {
  popup?: boolean;
  noopener?: boolean;
  noreferrer?: boolean;
} & XOR<{ width?: number }, { innerWidth?: number }> &
  XOR<{ height?: number }, { innerHeight?: number }> &
  XOR<{ left?: number }, { screenX?: number }> &
  XOR<{ top?: number }, { screenY?: number }>;

export function navigateTo(to: RouteLocationRaw | undefined | null, options?: NavigateToOptions) {
  if (!to) {
    to = "/";
  }

  const toPath =
    typeof to === "string" ? to : withQuery((to as RouteLocationPathRaw).path || "/", to.query || {}) + (to.hash || "");

  if (options?.open) {
    const { target = "_blank", windowFeatures = {} } = options.open;

    const features = Object.entries(windowFeatures)
      .filter(([_, value]) => value !== undefined)
      .map(([feature, value]) => `${feature.toLowerCase()}=${value}`)
      .join(", ");

    return globalThis.open(toPath, target, features);
  }

  if (options?.replace) {
    location.replace(toPath);
  } else {
    location.href = toPath;
  }
}
